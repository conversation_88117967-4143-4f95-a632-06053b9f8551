import React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from '@/client/components/ui';
import { ExternalLink, XIcon, ArrowUpDown } from 'lucide-react';
import { UnifiedToken } from '@/client/components/token-list';
import { formatCurrency } from '@/client/utils/formatters';
import { getNetworkIcon } from '@/utils/network-icons';
import TokenLogo from '@/client/components/token-logo';
import {
  useDetailedTokenInfo,
  formatTokenNumber,
  getTokenExplorerUrl,
} from '@/client/hooks/use-detailed-token-info';
import useMainScreenStore from '@/client/hooks/use-main-screen-store';
import useSwapStore from '@/client/hooks/use-swap-store';

interface TokenInfoDialogProps {
  token: UnifiedToken;
  onClose: () => void;
}

const TokenInfoDialog: React.FC<TokenInfoDialogProps> = ({
  token,
  onClose,
}) => {
  const {
    data: detailedInfo,
    isLoading,
    error,
  } = useDetailedTokenInfo({
    chain: token.chain,
    contractAddress: token.contractAddress,
    enabled: true,
  });

  const { setMainScreen } = useMainScreenStore();
  const { setTokenIn, reset } = useSwapStore();

  // Check if token is on HyperEVM network
  const isHyperEvmToken = token.chain === 'hyperevm';

  // Handle swap button click
  const handleSwapClick = () => {
    // Reset swap store to clear any previous state
    reset();

    // Set the current token as tokenIn for swap
    setTokenIn({
      chain: token.chain,
      chainName: token.chainName,
      symbol: token.symbol,
      name: token.name,
      balance: token.balance,
      balanceFormatted: token.balanceFormatted,
      usdValue: token.usdValue,
      usdPrice: token.usdPrice,
      contractAddress: token.contractAddress,
      decimals: token.decimals,
      isNative: token.isNative,
      logo: token.logo,
    });

    // Navigate to swap screen
    setMainScreen('swap');

    // Close the dialog
    onClose();
  };

  return (
    <DialogWrapper>
      <DialogHeader
        title="Token Information"
        onClose={onClose}
        icon={<XIcon className="size-4 text-white" />}
      />
      <DialogContent>
        {/* Token Header */}
        <div className="flex items-center gap-4 mb-6">
          <div className="relative">
            <TokenLogo
              symbol={token.symbol}
              existingLogo={token.logo}
              networkId={token.chain}
              tokenAddress={token.contractAddress}
              className="size-16 rounded-full"
            />
            <div className="absolute -bottom-1 -right-1 size-6 bg-[var(--background-color)] rounded-full flex items-center justify-center border border-[var(--card-color)]">
              <img
                src={getNetworkIcon(token.chain)}
                alt={token.chainName}
                className="size-4 rounded-full"
              />
            </div>
          </div>
          <div className="flex-1">
            <h3 className="text-xl font-bold text-white">{token.name}</h3>
            <p className="text-white/60">{token.symbol}</p>
            <p className="text-sm text-white/40">{token.chainName}</p>
          </div>
        </div>

        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : error ? (
          <div className="text-center py-8">
            <p className="text-red-400 mb-4">
              Failed to fetch detailed token information
            </p>
            <p className="text-white/60 text-sm">
              Showing basic information from your wallet
            </p>
          </div>
        ) : null}

        {/* Your Holdings */}
        <div className="bg-[var(--card-color)] rounded-lg p-4 mb-4">
          <h4 className="text-white font-medium mb-3">Your Holdings</h4>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-white/60 text-sm">Balance</p>
              <p className="text-white font-medium">
                {token.balanceFormatted.toFixed(4)} {token.symbol}
              </p>
            </div>
            <div>
              <p className="text-white/60 text-sm">Value</p>
              <p className="text-white font-medium">
                {(() => {
                  // Calculate USD value using latest price from API if available
                  if (detailedInfo?.price_usd) {
                    const latestPrice = parseFloat(detailedInfo.price_usd);
                    const calculatedValue =
                      token.balanceFormatted * latestPrice;
                    return formatCurrency(calculatedValue);
                  }
                  // Fallback to original value
                  return formatCurrency(token.usdValue);
                })()}
              </p>
            </div>
          </div>
        </div>

        {/* Market Information */}
        {detailedInfo && !error && (
          <div className="bg-[var(--card-color)] rounded-lg p-4 mb-4">
            <h4 className="text-white font-medium mb-3">Market Information</h4>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-white/60">Price</span>
                <span className="text-white font-medium">
                  {formatTokenNumber(detailedInfo.price_usd)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-white/60">Market Cap</span>
                <span className="text-white font-medium">
                  {formatTokenNumber(detailedInfo.market_cap_usd)}
                </span>
              </div>
              {detailedInfo.fdv_usd && detailedInfo.fdv_usd !== '0' && (
                <div className="flex justify-between">
                  <span className="text-white/60">FDV</span>
                  <span className="text-white font-medium">
                    {formatTokenNumber(detailedInfo.fdv_usd)}
                  </span>
                </div>
              )}
              <div className="flex justify-between">
                <span className="text-white/60">24h Volume</span>
                <span className="text-white font-medium">
                  {formatTokenNumber(detailedInfo.volume_usd.h24)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-white/60">Liquidity</span>
                <span className="text-white font-medium">
                  {formatTokenNumber(detailedInfo.total_reserve_in_usd)}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="space-y-2">
          {/* Swap button - only show for HyperEVM tokens */}
          {isHyperEvmToken && (
            <Button
              variant="primary"
              className="w-full flex items-center gap-2"
              onClick={handleSwapClick}
            >
              <ArrowUpDown className="size-4" />
              Swap {token.symbol}
            </Button>
          )}

          {getTokenExplorerUrl(
            token.chain,
            token.contractAddress,
            token.isNative
          ) && (
            <Button
              variant="secondary"
              className="w-full flex items-center gap-2"
              onClick={() =>
                window.open(
                  getTokenExplorerUrl(
                    token.chain,
                    token.contractAddress,
                    token.isNative
                  )!,
                  '_blank'
                )
              }
            >
              <ExternalLink className="size-4" />
              {token.isNative ? 'View Price Chart' : 'View on Explorer'}
            </Button>
          )}
        </div>
      </DialogContent>
    </DialogWrapper>
  );
};

export default TokenInfoDialog;
